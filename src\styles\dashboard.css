/* Dashboard Layout Styles - Matching Reference Images */

.dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header Styles */
.dashboard-header {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: white;
  padding: 12px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.5px;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-info {
  font-size: 14px;
  opacity: 0.9;
}

.connection-status {
  font-size: 12px;
  font-weight: 500;
}

.connection-status.text-success {
  color: #dcfce7;
}

.connection-status.text-warning {
  color: #fef3c7;
}

.connection-status.text-error {
  color: #fecaca;
}

.header-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.header-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.header-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

/* Main Content Area */
.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Map Panel - Top Section */
.map-panel {
  flex: 0 0 65%;
  display: flex;
  flex-direction: column;
  background: white;
  border-bottom: 2px solid #e2e8f0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  min-height: 50px;
}

.panel-header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Date Filter Styles in Panel Header */
.date-filter-container {
  display: flex;
  align-items: center;
}

.date-filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.date-filter-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.date-filter-label {
  font-size: 11px;
  font-weight: 600;
  color: #475569;
  white-space: nowrap;
}

.date-filter-input {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 11px;
  color: #374151;
  min-width: 150px;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.date-filter-input:focus {
  outline: none;
  border-color: #4ade80;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.1);
}

.date-filter-input.invalid {
  border-color: #ef4444;
  background-color: #fef2f2;
}

.date-filter-input.invalid:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.date-filter-error {
  color: #ef4444;
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.imei-selector,
.voyage-selector {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 13px;
  color: #374151;
  min-width: 150px;
  cursor: pointer;
}

.imei-selector {
  min-width: 180px;
  margin-right: 12px;
}

.imei-selector:focus,
.voyage-selector:focus {
  outline: none;
  border-color: #4ade80;
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* Message Panel - Bottom Section */
.message-panel {
  flex: 0 0 35%;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
}

.message-count {
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
}

.message-container {
  flex: 1;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .map-panel {
    flex: 0 0 60%;
  }
  
  .message-panel {
    flex: 0 0 40%;
  }
  
  .header-left {
    gap: 16px;
  }
  
  .app-title {
    font-size: 20px;
  }
  
  .voyage-selector {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 8px 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-left {
    justify-content: center;
  }
  
  .header-right {
    justify-content: center;
  }
  
  .panel-header {
    padding: 8px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .panel-header-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .date-filter-row {
    gap: 8px;
  }

  .date-filter-input {
    min-width: 140px;
    font-size: 10px;
  }
  
  .panel-controls {
    justify-content: center;
  }
  
  .voyage-selector {
    min-width: auto;
    width: 100%;
  }
  
  .map-panel {
    flex: 0 0 55%;
  }
  
  .message-panel {
    flex: 0 0 45%;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 18px;
  }

  .header-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .panel-title {
    font-size: 14px;
  }

  .date-filter-row {
    flex-direction: column;
    gap: 6px;
    align-items: stretch;
  }

  .date-filter-group {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }

  .date-filter-input {
    width: 100%;
    min-width: auto;
    font-size: 10px;
  }

  .date-filter-error {
    font-size: 9px;
    text-align: center;
  }
}

/* Loading and Error States */
.panel-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: #64748b;
  font-size: 14px;
}

.panel-error {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: #ef4444;
  font-size: 14px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  margin: 16px;
  border-radius: 8px;
  padding: 16px;
}

/* Smooth transitions */
.map-panel,
.message-panel {
  transition: flex 0.3s ease;
}

.panel-header {
  transition: all 0.2s ease;
}

/* Focus states for accessibility */
.voyage-selector:focus,
.header-btn:focus {
  outline: 2px solid #4ade80;
  outline-offset: 2px;
}
