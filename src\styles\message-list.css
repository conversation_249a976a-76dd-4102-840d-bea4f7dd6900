/* Message List Styles - Matching Reference Images */

.message-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.message-filters {
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  color: #374151;
  min-width: 120px;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #4ade80;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.1);
}

.table-container {
  flex: 1;
  overflow: auto;
  background: white;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  background: white;
}

.data-table thead {
  background: #f1f5f9;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table th {
  padding: 6px 8px;
  text-align: left;
  font-weight: 600;
  font-size: 10px;
  color: #475569;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  vertical-align: middle;
}

.sortable-header {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.sortable-header:hover {
  background: #e2e8f0;
}

.data-row {
  cursor: pointer;
  transition: background-color 0.15s ease;
  border-bottom: 1px solid #f1f5f9;
}

.data-row:hover {
  background: #f8fafc;
}

.data-row.selected-row {
  background: #dcfce7;
  border-color: #4ade80;
}

.data-table td {
  padding: 4px 8px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
  font-size: 11px;
  text-align: left;
}

.timestamp-cell {
  min-width: 120px;
  text-align: left;
}

.timestamp-content {
  display: inline;
  white-space: nowrap;
}

.timestamp-content .date {
  font-weight: 500;
  color: #1e293b;
  font-size: 11px;
}

.timestamp-content .time {
  color: #64748b;
  font-size: 10px;
  margin-left: 8px;
}

.coordinate-cell {
  min-width: 140px;
  font-family: 'Courier New', monospace;
  text-align: left;
}

.coordinate-content {
  display: inline;
  font-size: 10px;
  color: #374151;
  white-space: nowrap;
}

.speed-cell {
  text-align: left;
  font-weight: 500;
  color: #1e293b;
  min-width: 60px;
}

.direction-cell {
  text-align: left;
  color: #64748b;
  min-width: 50px;
}

.gps-cell {
  text-align: left;
  min-width: 60px;
}

.battery-cell {
  text-align: left;
  font-weight: 500;
  color: #1e293b;
  min-width: 60px;
}

.state-cell {
  text-align: left;
  min-width: 80px;
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.fixed {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.no-fix {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.battery-cell {
  text-align: center;
  font-weight: 500;
  color: #1e293b;
  min-width: 50px;
}



.no-data {
  text-align: center;
  padding: 32px;
  color: #64748b;
  font-style: italic;
  background: #f8fafc;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .data-table {
    font-size: 10px;
  }
  
  .data-table th {
    padding: 4px 6px;
    font-size: 9px;
  }

  .data-table td {
    padding: 3px 6px;
    font-size: 10px;
  }

  .timestamp-content .date,
  .coordinate-content {
    font-size: 9px;
  }

  .timestamp-content .time {
    font-size: 8px;
  }
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .filter-select {
    width: 100%;
    min-width: auto;
  }
  
  .data-table {
    font-size: 9px;
  }
  
  .data-table th {
    padding: 3px 4px;
    font-size: 8px;
  }

  .data-table td {
    padding: 2px 4px;
    font-size: 9px;
  }
  
  /* Hide some columns on mobile */
  .data-table th:nth-child(2),
  .data-table td:nth-child(2),
  .data-table th:nth-child(5),
  .data-table td:nth-child(5) {
    display: none;
  }
}

/* Scrollbar styling */
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.table-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading state */
.table-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #64748b;
  font-size: 12px;
}

/* Alternating row colors for better readability */
.data-row:nth-child(even) {
  background: #fafbfc;
}

.data-row:nth-child(even):hover {
  background: #f1f5f9;
}

.data-row.selected-row:nth-child(even) {
  background: #dcfce7;
}
