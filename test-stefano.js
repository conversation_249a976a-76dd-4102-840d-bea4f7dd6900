import WebSocket from 'ws';

console.log('🚀 Test GPS per utente Stefano (IMEI: 1234567890)');

const ws = new WebSocket('ws://localhost:8090/ws');

// Coordinate per un viaggio di test in Italia (Roma area)
const testRoute = [
  // Viaggio 1: Milano - Bologna (Inizio)
  { imei: '1234567890', lat: 45.4642, lng: 9.1900, speed: 0, status: 'Inizio' },        // Milano centro
  { imei: '1234567890', lat: 45.4146, lng: 9.2821, speed: 110, status: 'GPS Fixed' },   // Milano Sud
  { imei: '1234567890', lat: 44.9097, lng: 9.8461, speed: 125, status: 'GPS Fixed' },   // Piacenza
  { imei: '1234567890', lat: 44.7007, lng: 10.6337, speed: 120, status: 'GPS Fixed' },  // Parma
  { imei: '1234567890', lat: 44.5075, lng: 11.3514, speed: 0, status: 'Fine' },         // Bologna

  // Viaggio 2: Bologna - Firenze (<PERSON>ppennini)
  { imei: '1234567890', lat: 44.5075, lng: 11.3514, speed: 0, status: 'Inizio' },       // Bologna
  { imei: '1234567890', lat: 44.3808, lng: 11.2373, speed: 85, status: 'GPS Fixed' },   // Sasso Marconi
  { imei: '1234567890', lat: 44.2106, lng: 11.1298, speed: 65, status: 'GPS Fixed' },   // Appennini
  { imei: '1234567890', lat: 44.0565, lng: 11.0784, speed: 70, status: 'GPS Fixed' },   // Barberino
  { imei: '1234567890', lat: 43.8771, lng: 11.2775, speed: 0, status: 'Fine' },         // Firenze Nord

  // Viaggio 3: Firenze - Prato - Pistoia (giro turistico)
  { imei: '1234567890', lat: 43.8771, lng: 11.2775, speed: 0, status: 'Inizio' },       // Firenze Nord
  { imei: '1234567890', lat: 43.8796, lng: 11.0962, speed: 90, status: 'GPS Fixed' },   // Verso Prato
  { imei: '1234567890', lat: 43.8777, lng: 11.0187, speed: 50, status: 'GPS Fixed' },   // Prato
  { imei: '1234567890', lat: 43.9303, lng: 10.9079, speed: 85, status: 'GPS Fixed' },   // Verso Pistoia
  { imei: '1234567890', lat: 43.9332, lng: 10.9126, speed: 0, status: 'Fine' },         // Pistoia

  // Viaggio 4: Ritorno a Firenze centro
  { imei: '1234567890', lat: 43.9332, lng: 10.9126, speed: 0, status: 'Inizio' },       // Pistoia
  { imei: '1234567890', lat: 43.8777, lng: 11.0187, speed: 95, status: 'GPS Fixed' },   // Prato di nuovo
  { imei: '1234567890', lat: 43.8054, lng: 11.2051, speed: 75, status: 'GPS Fixed' },   // Periferia Firenze
  { imei: '1234567890', lat: 43.7792, lng: 11.2462, speed: 35, status: 'GPS Fixed' },   // Firenze centro
  { imei: '1234567890', lat: 43.7696, lng: 11.2558, speed: 0, status: 'Fine' }          // Duomo di Firenze
];

let currentPosition = 0;
let intervalId;

ws.on('open', () => {
  console.log('✅ Connesso al server GPS');
  
  // Inizia la simulazione del viaggio
  console.log('🚗 Inizio simulazione viaggio per Stefano...');
  
  function sendNextPosition() {
    if (currentPosition < testRoute.length) {
      const position = testRoute[currentPosition];
      
      const message = {
        type: 'gps_data',
        data: {
          imei: position.imei,
          timestamp: new Date().toISOString(),
          lat: position.lat,
          lng: position.lng,
          speed: position.speed,
          status: position.status,
          battery: Math.max(95 - currentPosition * 2, 70) // Batteria che diminuisce
        }
      };
      
      console.log(`📍 Posizione ${currentPosition + 1}/${testRoute.length}: ${position.lat}, ${position.lng} - ${position.status} - ${position.speed} km/h`);
      ws.send(JSON.stringify(message));
      
      currentPosition++;
      
      if (currentPosition < testRoute.length) {
        // Invia la prossima posizione dopo 3 secondi
        setTimeout(sendNextPosition, 3000);
      } else {
        console.log('🏁 Viaggio completato!');
        setTimeout(() => {
          console.log('🔚 Chiusura connessione');
          ws.close();
          process.exit(0);
        }, 2000);
      }
    }
  }
  
  // Inizia con la prima posizione
  sendNextPosition();
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📥 Risposta server:', message.type);
  } catch (error) {
    console.log('📥 Messaggio ricevuto:', data.toString());
  }
});

ws.on('close', () => {
  console.log('❌ Disconnesso dal server');
});

ws.on('error', (error) => {
  console.error('🚨 Errore WebSocket:', error.message);
  process.exit(1);
});

// Gestione interruzione
process.on('SIGINT', () => {
  console.log('\n🛑 Interruzione manuale');
  if (intervalId) clearInterval(intervalId);
  ws.close();
  process.exit(0);
});
