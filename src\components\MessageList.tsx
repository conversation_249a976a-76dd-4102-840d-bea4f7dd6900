import React, { useState, useMemo } from 'react';
import { GPSMessage, Voyage } from '../types';
import '../styles/message-list.css';

interface MessageListProps {
  messages: GPSMessage[];
  selectedMessage: GPSMessage | null;
  selectedVoyage: string | null;
  selectedImei: string;
  voyages: Voyage[];
  onMessageSelect: (message: GPSMessage) => void;
}

type SortField = 'trackerTimestamp' | 'serverTimestamp' | 'imei' | 'speed' | 'status' | 'batteryLevel';
type SortDirection = 'asc' | 'desc';

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  selectedMessage,
  selectedVoyage,
  selectedImei,
  voyages,
  onMessageSelect
}) => {
  const [sortField, setSortField] = useState<SortField>('trackerTimestamp');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Messages are already filtered by IMEI in parent component
  // Apply sorting only
  const sortedMessages = useMemo(() => {
    return messages.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      // Handle date sorting
      if (sortField === 'trackerTimestamp' || sortField === 'serverTimestamp') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [messages, sortField, sortDirection]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString()
    };
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Inizio':
        return 'bg-success text-white';
      case 'Fine':
        return 'bg-error text-white';
      case 'GPS Fixed':
        return 'bg-primary-green text-white';
      case 'No GPS Fixed':
        return 'bg-warning text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // No filters needed - data is already filtered by IMEI in parent

  return (
    <div className="message-list-container">
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th
                onClick={() => handleSort('trackerTimestamp')}
                className="sortable-header"
              >
                DATA DISPOSITIVO {getSortIcon('trackerTimestamp')}
              </th>
              <th
                onClick={() => handleSort('serverTimestamp')}
                className="sortable-header"
              >
                DATA SERVER {getSortIcon('serverTimestamp')}
              </th>
              <th
                onClick={() => handleSort('imei')}
                className="sortable-header"
              >
                COORDINATE {getSortIcon('imei')}
              </th>
              <th
                onClick={() => handleSort('speed')}
                className="sortable-header"
              >
                VELOCITÀ {getSortIcon('speed')}
              </th>
              <th
                onClick={() => handleSort('batteryLevel')}
                className="sortable-header"
              >
                BATTERIA {getSortIcon('batteryLevel')}
              </th>
              <th
                onClick={() => handleSort('status')}
                className="sortable-header"
              >
                STATO {getSortIcon('status')}
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedMessages.length === 0 ? (
              <tr>
                <td colSpan={6} className="no-data">
                  Nessun messaggio da visualizzare
                </td>
              </tr>
            ) : (
              sortedMessages.map((message) => {
                const trackerTime = formatTimestamp(message.trackerTimestamp);
                const serverTime = formatTimestamp(message.serverTimestamp);
                const isSelected = selectedMessage?.id === message.id;

                return (
                  <tr
                    key={message.id}
                    onClick={() => onMessageSelect(message)}
                    className={`data-row ${isSelected ? 'selected-row' : ''}`}
                  >
                    <td className="timestamp-cell">
                      <div className="timestamp-content">
                        <span className="date">{trackerTime.date}</span>
                        <span className="time">{trackerTime.time}</span>
                      </div>
                    </td>
                    <td className="timestamp-cell">
                      <div className="timestamp-content">
                        <span className="date">{serverTime.date}</span>
                        <span className="time">{serverTime.time}</span>
                      </div>
                    </td>
                    <td className="coordinate-cell">
                      <div className="coordinate-content">
                        {message.latitude.toFixed(6)}, {message.longitude.toFixed(6)}
                      </div>
                    </td>
                    <td className="speed-cell">{message.speed} km/h</td>
                    <td className="battery-cell">{message.batteryLevel}%</td>
                    <td className="state-cell">
                      <span className={`status-badge ${getStatusBadgeClass(message.status)}`}>
                        {message.status}
                      </span>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};
